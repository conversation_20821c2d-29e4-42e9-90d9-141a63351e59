import sqlite3
import threading
from typing import Dict, Optional, List
from contextlib import contextmanager
import os
import json
from datetime import datetime
import enum

# 全局变量：内存中的 appId -> callback_url 映射
callback_map: Dict[str, str] = {}
callback_map_lock = threading.RLock()

# 数据库文件路径
DB_PATH = "data/callbacks.db"

class TaskStatus(enum.Enum):
    """任务状态枚举"""
    PENDING = "PENDING"
    RUNNING = "RUNNING" 
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"
    REVOKED = "REVOKED"
    RETRY = "RETRY"

class CallbackStatus(enum.Enum):
    """回调状态枚举"""
    NOT_CALLED = "NOT_CALLED"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"
    ABANDONED = "ABANDONED"

class CallbackDB:
    """回调数据库管理类"""
    
    def __init__(self, db_path: str = DB_PATH):
        self.db_path = db_path
        self._init_database()
    
    def _init_database(self):
        """初始化数据库表"""
        with self._get_connection() as conn:
            # 原有的回调配置表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS app_callbacks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    app_id TEXT UNIQUE NOT NULL,
                    callback_url TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_app_id ON app_callbacks(app_id)
            """)
            
            # 新增任务监听队列表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS task_monitoring (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id TEXT UNIQUE NOT NULL,
                    app_id TEXT NOT NULL,
                    queue_name TEXT NOT NULL,
                    inputs TEXT NOT NULL,
                    task_status TEXT NOT NULL DEFAULT 'PENDING',
                    callback_status TEXT NOT NULL DEFAULT 'NOT_CALLED',
                    callback_attempts INTEGER DEFAULT 0,
                    last_callback_attempt TIMESTAMP,
                    task_result TEXT,
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_task_id ON task_monitoring(task_id)
            """)
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_app_id_monitoring ON task_monitoring(app_id)
            """)
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_task_status ON task_monitoring(task_status)
            """)
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_callback_status ON task_monitoring(callback_status)
            """)
            
            conn.commit()
    
    @contextmanager
    def _get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
        finally:
            conn.close()
    
    def save_callback(self, app_id: str, callback_url: str) -> bool:
        """保存或更新回调配置"""
        try:
            with self._get_connection() as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO app_callbacks 
                    (app_id, callback_url, updated_at) 
                    VALUES (?, ?, CURRENT_TIMESTAMP)
                """, (app_id, callback_url))
                conn.commit()
            return True
        except Exception as e:
            print(f"保存回调配置失败: {e}")
            return False
    
    def get_callback(self, app_id: str) -> Optional[str]:
        """获取指定appId的回调地址"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute(
                    "SELECT callback_url FROM app_callbacks WHERE app_id = ?",
                    (app_id,)
                )
                row = cursor.fetchone()
                return row[0] if row else None
        except Exception as e:
            print(f"获取回调配置失败: {e}")
            return None
    
    def get_all_callbacks(self) -> Dict[str, str]:
        """获取所有回调配置"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute("SELECT app_id, callback_url FROM app_callbacks")
                return {row[0]: row[1] for row in cursor.fetchall()}
        except Exception as e:
            print(f"获取所有回调配置失败: {e}")
            return {}
    
    def delete_callback(self, app_id: str) -> bool:
        """删除指定appId的回调配置"""
        try:
            with self._get_connection() as conn:
                conn.execute("DELETE FROM app_callbacks WHERE app_id = ?", (app_id,))
                conn.commit()
            return True
        except Exception as e:
            print(f"删除回调配置失败: {e}")
            return False

    # 任务监听相关方法
    def add_task_to_monitoring(self, task_id: str, app_id: str, queue_name: str, inputs: dict) -> bool:
        """添加任务到监听队列"""
        try:
            with self._get_connection() as conn:
                conn.execute("""
                    INSERT INTO task_monitoring 
                    (task_id, app_id, queue_name, inputs, task_status, callback_status) 
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (task_id, app_id, queue_name, json.dumps(inputs), 
                      TaskStatus.PENDING.value, CallbackStatus.NOT_CALLED.value))
                conn.commit()
            return True
        except Exception as e:
            print(f"添加任务到监听队列失败: {e}")
            return False
    
    def update_task_status(self, task_id: str, status: TaskStatus, result: str = None, error: str = None) -> bool:
        """更新任务状态"""
        try:
            with self._get_connection() as conn:
                conn.execute("""
                    UPDATE task_monitoring 
                    SET task_status = ?, task_result = ?, error_message = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE task_id = ?
                """, (status.value, result, error, task_id))
                conn.commit()
            return True
        except Exception as e:
            print(f"更新任务状态失败: {e}")
            return False
    
    def update_callback_status(self, task_id: str, callback_status: CallbackStatus, attempts: int = None) -> bool:
        """更新回调状态"""
        try:
            with self._get_connection() as conn:
                if attempts is not None:
                    conn.execute("""
                        UPDATE task_monitoring 
                        SET callback_status = ?, callback_attempts = ?, 
                            last_callback_attempt = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
                        WHERE task_id = ?
                    """, (callback_status.value, attempts, task_id))
                else:
                    conn.execute("""
                        UPDATE task_monitoring 
                        SET callback_status = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE task_id = ?
                    """, (callback_status.value, task_id))
                conn.commit()
            return True
        except Exception as e:
            print(f"更新回调状态失败: {e}")
            return False
    
    def get_tasks_needing_callback(self) -> List[Dict]:
        """获取需要回调的任务"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute("""
                    SELECT * FROM task_monitoring 
                    WHERE task_status IN ('SUCCESS', 'FAILURE', 'REVOKED') 
                    AND callback_status IN ('NOT_CALLED', 'FAILED')
                    AND callback_attempts < 3
                """)
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取需要回调的任务失败: {e}")
            return []
    
    def get_pending_tasks(self) -> List[Dict]:
        """获取待监控的任务"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute("""
                    SELECT * FROM task_monitoring 
                    WHERE task_status IN ('PENDING', 'RUNNING', 'RETRY')
                """)
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取待监控任务失败: {e}")
            return []

    def get_task_by_id(self, task_id: str) -> Optional[Dict]:
        """根据任务ID获取任务信息"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute("""
                    SELECT * FROM task_monitoring WHERE task_id = ?
                """, (task_id,))
                row = cursor.fetchone()
                return dict(row) if row else None
        except Exception as e:
            print(f"获取任务信息失败: {e}")
            return None

# 全局数据库实例
db = CallbackDB()

def load_callbacks_to_memory():
    """从数据库加载所有回调配置到内存"""
    global callback_map
    with callback_map_lock:
        callback_map = db.get_all_callbacks()
        print(f"已加载 {len(callback_map)} 个回调配置到内存")

def save_callback_config(app_id: str, callback_url: str) -> bool:
    """保存回调配置（同时存储到数据库和内存）"""
    # 保存到数据库
    if not db.save_callback(app_id, callback_url):
        return False
    
    # 更新内存
    with callback_map_lock:
        callback_map[app_id] = callback_url
    
    return True

def get_callback_url(app_id: str) -> Optional[str]:
    """从内存获取回调地址"""
    with callback_map_lock:
        return callback_map.get(app_id)

def get_all_callback_configs() -> Dict[str, str]:
    """获取所有回调配置"""
    with callback_map_lock:
        return callback_map.copy()

def delete_callback_config(app_id: str) -> bool:
    """删除回调配置（同时从数据库和内存删除）"""
    # 从数据库删除
    if not db.delete_callback(app_id):
        return False
    
    # 从内存删除
    with callback_map_lock:
        callback_map.pop(app_id, None)
    
    return True 

def add_task_to_monitoring_queue(task_id: str, app_id: str, queue_name: str, inputs: dict) -> bool:
    """添加任务到监听队列"""
    return db.add_task_to_monitoring(task_id, app_id, queue_name, inputs)

def update_task_status_in_db(task_id: str, status: TaskStatus, result: str = None, error: str = None) -> bool:
    """更新任务状态"""
    return db.update_task_status(task_id, status, result, error)

def update_callback_status_in_db(task_id: str, callback_status: CallbackStatus, attempts: int = None) -> bool:
    """更新回调状态"""
    return db.update_callback_status(task_id, callback_status, attempts)

def get_tasks_needing_callback() -> List[Dict]:
    """获取需要回调的任务"""
    return db.get_tasks_needing_callback()

def get_pending_tasks_from_db() -> List[Dict]:
    """获取待监控的任务"""
    return db.get_pending_tasks()

def get_task_info(task_id: str) -> Optional[Dict]:
    """获取任务信息"""
    return db.get_task_by_id(task_id) 