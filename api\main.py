from celery import Celery
from contextlib import asynccontextmanager
from fastapi import Depends, FastAPI
from api import task_router
from api.database import load_callbacks_to_memory
from api.task_monitor import start_task_monitor, stop_task_monitor

from api.dependencies import get_celery_app
from fastapi.middleware.cors import CORSMiddleware

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    print("正在启动应用...")
    
    # 从数据库加载所有回调配置到内存
    load_callbacks_to_memory()
    
    # 启动任务监控器
    print("正在启动任务监控器...")
    start_task_monitor()
    
    print("应用启动完成")
    
    yield
    
    # 关闭时执行
    print("应用正在关闭...")
    print("正在停止任务监控器...")
    stop_task_monitor()
    print("应用关闭完成")

app = FastAPI(
    title="Celery Task API",
    description="Automatically generated API for Celery tasks",
    version="1.0.0",
    lifespan=lifespan,
)

# 添加CORS支持
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含任务路由
app.include_router(task_router.router)

# 添加任务状态检查端点
@app.get("/tasks/{task_id}/status", tags=["Task Monitoring"])
def get_task_status(task_id: str, celery_app: Celery = Depends(get_celery_app)):
    result = celery_app.AsyncResult(task_id)
    return {
        "task_id": task_id,
        "status": result.status,
        "result": result.result if result.ready() else None
    }

# 健康检查端点
@app.get("/health", tags=["System"])
def health_check():
    return {"status": "healthy", "celery": "connected"}

# 添加运行代码
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)