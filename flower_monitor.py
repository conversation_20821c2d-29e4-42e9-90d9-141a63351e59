import os
import subprocess
import sys


CELERY_BROKER_URL = os.getenv('CELERY_BROKER_URL', "redis://:celeryhub123@localhost:6389/0")

def start_flower():
    """使用subprocess启动Flower监控"""
    try:
        # 构建celery flower命令
        cmd = [
            sys.executable, '-m', 'celery',
            '-A', 'sskw.tasks.celery_app.app',
            'flower',
            '--port=5555',
             '--inspect_timeout=10000', #超时时间
            f'--broker={CELERY_BROKER_URL}',
            '--loglevel=info',
            '--basic_auth=celeryhub:celeryhub'
        ]
        
        print(f"正在启动Flower监控...")
        print(f"访问地址: http://localhost:5555")
        print(f"Broker地址: {CELERY_BROKER_URL}")
        print(f"执行命令: {' '.join(cmd)}")
        
        # 启动flower进程
        process = subprocess.run(cmd, check=True)
        
    except subprocess.CalledProcessError as e:
        print(f"启动Flower失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n停止Flower监控...")
        sys.exit(0)

if __name__ == '__main__':
    start_flower()