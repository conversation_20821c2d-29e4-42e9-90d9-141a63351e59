services:
  # Redis 服务
  celeryhub-redis:
    image: redis:7-alpine
    container_name: celeryhub-redis
    ports:
      - "6389:6379"
    volumes:
      - ./data/redis:/data
    command: redis-server --appendonly yes --requirepass celeryhub123
    restart: unless-stopped
    networks:
      - celeryhub_network
    environment:
      - TZ=Asia/Shanghai
    
  
  celeryhub-api:
    image: celeryhub-api:0.2.2
    container_name: celeryhub-api
    ports:
      - "8000:8000"
    volumes:
      - ./data/api:/app/data

    restart: unless-stopped
    networks:
      - celeryhub_network
    environment:
      - CELERY_BROKER_URL=redis://:celeryhub123@celeryhub-redis:6379/0
      - CELERY_RESULT_BACKEND=redis://:celeryhub123@celeryhub-redis:6379/1
      - TZ=Asia/Shanghai
    depends_on:
      - celeryhub-redis

  celeryhub-flower:
    image: celeryhub-api:0.2.2
    container_name: celeryhub-flower
    ports:
      - "5555:5555"
    restart: unless-stopped
    networks:
      - celeryhub_network

    environment:
      - CELERY_RESULT_BACKEND=redis://:celeryhub123@celeryhub-redis:6379/1
      - CELERY_BROKER_URL=redis://:celeryhub123@celeryhub-redis:6379/0
      - TZ=Asia/Shanghai
      
    depends_on:
      - celeryhub-api
      - celeryhub-redis

    command: python flower_monitor.py

networks:
  celeryhub_network:
    driver: bridge
