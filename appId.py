import os
import base64
import hashlib
import uuid
from datetime import datetime

def generate_app_id(prefix="APP"):
    """
    生成 AppId
    :param prefix: 可选前缀，如公司/项目缩写
    :return: app_id (通常16-32字符)
    """
    # 方法1: UUID版本4 (32字符)
    # app_id = prefix + uuid.uuid4().hex
    
    # 方法2: 时间戳+随机数 (更可控长度)
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    rand_str = base64.b32encode(os.urandom(6)).decode('utf-8').replace('=', '')
    app_id = f"{prefix}_{timestamp}_{rand_str}"[:32]  # 限制最大32字符
    
    return app_id

def generate_app_key(length=32):
    """
    生成安全的 AppKey/Secret
    :param length: 密钥长度(推荐32-64)
    :return: app_key
    """
    if length < 16:
        raise ValueError("AppKey length should be at least 16 characters")
    
    # 使用加密安全的随机数生成器
    random_bytes = os.urandom(length)
    app_key = base64.urlsafe_b64encode(random_bytes).decode('utf-8')[:length]
    
    return app_key

# 示例使用
if __name__ == "__main__":
    app_id = generate_app_id()
    app_key = generate_app_key()
    
    print(f"Generated AppId: {app_id} (Length: {len(app_id)})")
    print(f"Generated AppKey: {app_key} (Length: {len(app_key)})")