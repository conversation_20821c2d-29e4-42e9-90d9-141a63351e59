import asyncio
import threading
import time
import json
import requests
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import logging

from api.database import (
    TaskStatus, CallbackStatus, 
    get_tasks_needing_callback, get_pending_tasks_from_db,
    update_task_status_in_db, update_callback_status_in_db,
    get_callback_url, get_task_info
)
from api.dependencies import get_celery_app

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TaskMonitor:
    """任务监听器"""
    
    def __init__(self, check_interval: int = 5, callback_timeout: int = 10):
        self.check_interval = check_interval  # 检查间隔（秒）
        self.callback_timeout = callback_timeout  # 回调超时时间（秒）
        self.running = False
        self.monitor_thread = None
        self.callback_thread = None
        self.celery_app = get_celery_app()
    
    def start(self):
        """启动监听器"""
        if self.running:
            logger.warning("监听器已经在运行")
            return
        
        self.running = True
        
        # 启动任务状态监控线程
        self.monitor_thread = threading.Thread(target=self._monitor_tasks, daemon=True)
        self.monitor_thread.start()
        
        # 启动回调处理线程
        self.callback_thread = threading.Thread(target=self._process_callbacks, daemon=True)
        self.callback_thread.start()
        
        logger.info("任务监听器已启动")
    
    def stop(self):
        """停止监听器"""
        self.running = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        if self.callback_thread and self.callback_thread.is_alive():
            self.callback_thread.join(timeout=5)
        
        logger.info("任务监听器已停止")
    
    def _monitor_tasks(self):
        """监控任务状态变化"""
        logger.info("任务状态监控线程已启动")
        
        while self.running:
            try:
                # 获取待监控的任务
                pending_tasks = get_pending_tasks_from_db()
                
                for task_info in pending_tasks:
                    self._check_task_status(task_info)
                
                # 等待下次检查
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"监控任务时发生错误: {e}")
                time.sleep(self.check_interval)
    
    def _check_task_status(self, task_info: Dict):
        """检查单个任务的状态"""
        task_id = task_info['task_id']
        current_status = task_info['task_status']
        
        try:
            # 从Celery获取任务状态
            result = self.celery_app.AsyncResult(task_id)
            celery_status = result.status
            
            # 映射Celery状态到我们的状态
            new_status = self._map_celery_status(celery_status)
            
            # 如果状态发生变化，更新数据库
            if new_status.value != current_status:
                task_result = None
                error_message = None
                
                if result.ready():
                    if result.successful():
                        task_result = json.dumps(result.result) if result.result else None
                    else:
                        error_message = str(result.info) if result.info else "任务执行失败"
                
                success = update_task_status_in_db(
                    task_id, new_status, task_result, error_message
                )
                
                if success:
                    logger.info(f"任务 {task_id} 状态更新: {current_status} -> {new_status.value}")
                else:
                    logger.error(f"更新任务 {task_id} 状态失败")
                    
        except Exception as e:
            logger.error(f"检查任务 {task_id} 状态时发生错误: {e}")
    
    def _map_celery_status(self, celery_status: str) -> TaskStatus:
        """映射Celery状态到TaskStatus"""
        status_mapping = {
            'PENDING': TaskStatus.PENDING,
            'STARTED': TaskStatus.RUNNING,
            'SUCCESS': TaskStatus.SUCCESS,
            'FAILURE': TaskStatus.FAILURE,
            'REVOKED': TaskStatus.REVOKED,
            'RETRY': TaskStatus.RETRY
        }
        return status_mapping.get(celery_status, TaskStatus.PENDING)
    
    def _process_callbacks(self):
        """处理回调请求"""
        logger.info("回调处理线程已启动")
        
        while self.running:
            try:
                # 获取需要回调的任务
                tasks_needing_callback = get_tasks_needing_callback()
                
                for task_info in tasks_needing_callback:
                    self._execute_callback(task_info)
                
                # 等待下次处理
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"处理回调时发生错误: {e}")
                time.sleep(self.check_interval)
    
    def _execute_callback(self, task_info: Dict):
        """执行单个任务的回调"""
        task_id = task_info['task_id']
        app_id = task_info['app_id']
        current_attempts = task_info['callback_attempts']
        
        # 获取回调URL
        callback_url = get_callback_url(app_id)
        if not callback_url:
            logger.warning(f"任务 {task_id} 的应用 {app_id} 没有配置回调地址")
            update_callback_status_in_db(task_id, CallbackStatus.ABANDONED)
            return
        
        # 准备回调数据
        callback_data = self._prepare_callback_data(task_info)
        
        # 执行回调
        success = self._make_callback_request(callback_url, callback_data)
        
        # 更新回调状态
        new_attempts = current_attempts + 1
        
        if success:
            update_callback_status_in_db(task_id, CallbackStatus.SUCCESS, new_attempts)
            logger.info(f"任务 {task_id} 回调成功")
        else:
            if new_attempts >= 3:
                update_callback_status_in_db(task_id, CallbackStatus.ABANDONED, new_attempts)
                logger.warning(f"任务 {task_id} 回调失败，已达到最大重试次数，放弃回调")
            else:
                update_callback_status_in_db(task_id, CallbackStatus.FAILED, new_attempts)
                logger.warning(f"任务 {task_id} 回调失败，将重试 (尝试次数: {new_attempts}/3)")
    
    def _prepare_callback_data(self, task_info: Dict) -> Dict:
        """准备回调数据"""
        return {
            "task_id": task_info['task_id'],
            "app_id": task_info['app_id'],
            "queue_name": task_info['queue_name'],
            "inputs": json.loads(task_info['inputs']) if task_info['inputs'] else {},
            "status": task_info['task_status'],
            "result": json.loads(task_info['task_result']) if task_info['task_result'] else None,
            "error": task_info['error_message'],
            "timestamp": datetime.now().isoformat(),
            "callback_attempts": task_info['callback_attempts'] + 1
        }
    
    def _make_callback_request(self, callback_url: str, data: Dict) -> bool:
        """发送回调请求"""
        try:
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'CeleryHub-Callback/1.0'
            }
            
            response = requests.post(
                callback_url,
                json=data,
                headers=headers,
                timeout=self.callback_timeout
            )
            
            # 认为2xx状态码为成功
            if 200 <= response.status_code < 300:
                logger.info(f"回调成功: {callback_url}, 状态码: {response.status_code}")
                return True
            else:
                logger.warning(f"回调失败: {callback_url}, 状态码: {response.status_code}, 响应: {response.text}")
                return False
                
        except requests.exceptions.Timeout:
            logger.warning(f"回调超时: {callback_url}")
            return False
        except requests.exceptions.RequestException as e:
            logger.warning(f"回调请求异常: {callback_url}, 错误: {e}")
            return False
        except Exception as e:
            logger.error(f"回调过程中发生未知错误: {callback_url}, 错误: {e}")
            return False

# 全局监听器实例
task_monitor = TaskMonitor()

def start_task_monitor():
    """启动任务监听器"""
    task_monitor.start()

def stop_task_monitor():
    """停止任务监听器"""
    task_monitor.stop()

def get_monitor_status() -> Dict:
    """获取监听器状态"""
    return {
        "running": task_monitor.running,
        "check_interval": task_monitor.check_interval,
        "callback_timeout": task_monitor.callback_timeout,
        "monitor_thread_alive": task_monitor.monitor_thread.is_alive() if task_monitor.monitor_thread else False,
        "callback_thread_alive": task_monitor.callback_thread.is_alive() if task_monitor.callback_thread else False
    } 